-- 跨数据库查询示例SQL
-- 用于演示如何在PostgreSQL中进行跨数据库JOIN查询

-- 1. 基础的跨数据库查询
-- 查询面单商品及其对应的订单项信息
SELECT 
    wp.id as waybill_product_id,
    wp.waybill_code,
    wp.order_id,
    wp.sku_id as waybill_sku_id,
    wp.outer_oi_id,
    wp.product_name as waybill_product_name,
    oi.ex_sub_order_id,
    oi.raw_product_name,
    oi.sku_id as order_sku_id,
    oi.item_num,
    oi.goods_price,
    oi.order_status,
    oi.order_status_desc
FROM cert_waybill_product wp
LEFT JOIN zhulinks_orders.orders_orderitems oi ON wp.outer_oi_id = oi.ex_sub_order_id
WHERE wp.bind_status = 2
  AND wp.is_deleted = false
  AND wp.company_id = $1;

-- 2. 复杂的多表JOIN查询
-- 包含所有相关业务表的完整信息查询
SELECT 
    -- 面单商品信息
    wp.id as waybill_product_id,
    wp.waybill_code,
    wp.order_id,
    wp.sku_id as waybill_sku_id,
    wp.outer_oi_id,
    wp.oi_id,
    wp.product_name as waybill_product_name,
    wp.quantity as waybill_quantity,
    wp.bind_status,
    wp.product_sn,
    wp.created_at as waybill_product_created_at,
    
    -- 面单信息
    sw.id as shipping_waybill_id,
    sw.waybill_code as shipping_waybill_code,
    sw.logistics_company,
    sw.send_date,
    sw.total_qty,
    sw.shipping_user_name,
    sw.inbound_order_code,
    sw.inbound_order_detail_code,
    
    -- 入库单明细信息
    iod.id as inbound_order_detail_id,
    iod.code as inbound_order_detail_code,
    iod.shipping_type,
    iod.cert_type,
    iod.batch_count,
    iod.product_count as detail_product_count,
    iod.cert_count,
    
    -- 入库单信息
    io.id as inbound_order_id,
    io.code as inbound_order_code,
    io.check_company_name,
    io.product_count as order_product_count,
    io.waybill_count,
    io.status as inbound_order_status,
    
    -- 发货人信息
    su.id as shipping_user_table_id,
    su.user_id as shipping_user_id,
    su.username as shipping_username,
    su.real_name as shipping_real_name,
    su.mobile as shipping_mobile,
    su.status as shipping_user_status,
    
    -- 清点人员信息
    cu.id as check_user_table_id,
    cu.user_id as check_user_id,
    cu.username as check_username,
    cu.real_name as check_real_name,
    cu.mobile as check_mobile,
    
    -- 公司信息
    c.id as company_table_id,
    c.company_id,
    c.name as company_name,
    c.contact_user as company_contact_user,
    c.contact_phone as company_contact_phone,
    c.province_id,
    c.city_id,
    c.district_id,
    c.address as company_address,
    
    -- 订单项信息（来自orders数据库）
    oi.id as order_item_id,
    oi.ex_sub_order_id,
    oi.raw_product_name,
    oi.sku_id as order_sku_id,
    oi.item_num,
    oi.goods_price,
    oi.order_status,
    oi.order_status_desc,
    oi.order_amount,
    oi.pay_amount,
    oi.paid_amount,
    oi.cost_price_amount,
    oi.post_amount,
    oi.promotion_amount,
    oi.refund_status,
    oi.after_sale_status,
    oi.after_sale_type,
    oi.ship_time,
    oi.logistics_info,
    oi.receiver_info,
    oi.other_info,
    oi.relate_order_id,
    oi.order_time,
    oi.pay_time
    
FROM cert_waybill_product wp

-- 关联面单表
INNER JOIN cert_shipping_waybill sw ON wp.waybill_id = sw.id

-- 关联入库单明细表
INNER JOIN cert_inbound_order_detail iod ON sw.inbound_order_detail_id = iod.id

-- 关联入库单表
INNER JOIN cert_inbound_order io ON iod.inbound_order_id = io.id

-- 关联发货人信息
LEFT JOIN cert_users su ON iod.shipping_user_id = su.id

-- 关联清点人员信息
LEFT JOIN cert_users cu ON io.check_user_id = cu.id

-- 关联公司信息
INNER JOIN cert_companies c ON wp.company_id = c.id

-- 跨数据库关联订单项表
LEFT JOIN zhulinks_orders.orders_orderitems oi ON wp.outer_oi_id = oi.ex_sub_order_id

WHERE wp.bind_status = $1
  AND wp.is_deleted = $2
  AND wp.company_id = $3
  AND iod.shipping_user_id = $4
  
ORDER BY wp.created_at DESC, wp.id DESC;

-- 3. 性能优化查询
-- 添加索引提示和查询优化
SELECT /*+ USE_INDEX(wp, idx_waybill_product_company_bind_status) */
    wp.id,
    wp.waybill_code,
    wp.sku_id,
    wp.outer_oi_id,
    oi.raw_product_name,
    oi.item_num,
    oi.goods_price
FROM cert_waybill_product wp
LEFT JOIN zhulinks_orders.orders_orderitems oi ON wp.outer_oi_id = oi.ex_sub_order_id
WHERE wp.bind_status = 2
  AND wp.is_deleted = false
  AND wp.company_id = $1
  AND EXISTS (
    SELECT 1 
    FROM cert_inbound_order_detail iod 
    WHERE iod.id = wp.inbound_order_detail_id 
      AND iod.shipping_user_id = $2
  )
ORDER BY wp.id DESC
LIMIT 100;

-- 4. 统计查询示例
-- 统计各种状态的面单数量
SELECT 
    wp.bind_status,
    COUNT(*) as total_count,
    COUNT(oi.id) as with_order_count,
    COUNT(*) - COUNT(oi.id) as without_order_count,
    AVG(oi.goods_price) as avg_price,
    SUM(oi.order_amount) as total_amount
FROM cert_waybill_product wp
LEFT JOIN zhulinks_orders.orders_orderitems oi ON wp.outer_oi_id = oi.ex_sub_order_id
INNER JOIN cert_shipping_waybill sw ON wp.waybill_id = sw.id
INNER JOIN cert_inbound_order_detail iod ON sw.inbound_order_detail_id = iod.id
WHERE wp.is_deleted = false
  AND wp.company_id = $1
  AND iod.shipping_user_id = $2
GROUP BY wp.bind_status
ORDER BY wp.bind_status;

-- 5. 分页查询示例
-- 支持分页的复杂查询
WITH filtered_waybill_products AS (
    SELECT wp.id
    FROM cert_waybill_product wp
    INNER JOIN cert_shipping_waybill sw ON wp.waybill_id = sw.id
    INNER JOIN cert_inbound_order_detail iod ON sw.inbound_order_detail_id = iod.id
    WHERE wp.bind_status = $1
      AND wp.is_deleted = $2
      AND wp.company_id = $3
      AND iod.shipping_user_id = $4
    ORDER BY wp.created_at DESC, wp.id DESC
    LIMIT $5 OFFSET $6
)
SELECT 
    wp.*,
    sw.logistics_company,
    iod.code as detail_code,
    oi.raw_product_name,
    oi.goods_price
FROM filtered_waybill_products fwp
INNER JOIN cert_waybill_product wp ON fwp.id = wp.id
INNER JOIN cert_shipping_waybill sw ON wp.waybill_id = sw.id
INNER JOIN cert_inbound_order_detail iod ON sw.inbound_order_detail_id = iod.id
LEFT JOIN zhulinks_orders.orders_orderitems oi ON wp.outer_oi_id = oi.ex_sub_order_id
ORDER BY wp.created_at DESC, wp.id DESC;

-- 6. 数据一致性检查查询
-- 检查面单商品与订单项的数据一致性
SELECT 
    'SKU不匹配' as issue_type,
    wp.id as waybill_product_id,
    wp.sku_id as waybill_sku,
    oi.sku_id as order_sku,
    wp.outer_oi_id
FROM cert_waybill_product wp
INNER JOIN zhulinks_orders.orders_orderitems oi ON wp.outer_oi_id = oi.ex_sub_order_id
WHERE wp.sku_id != oi.sku_id
  AND wp.company_id = $1

UNION ALL

SELECT 
    '缺少订单项' as issue_type,
    wp.id as waybill_product_id,
    wp.sku_id as waybill_sku,
    NULL as order_sku,
    wp.outer_oi_id
FROM cert_waybill_product wp
LEFT JOIN zhulinks_orders.orders_orderitems oi ON wp.outer_oi_id = oi.ex_sub_order_id
WHERE oi.id IS NULL
  AND wp.outer_oi_id IS NOT NULL
  AND wp.company_id = $1;

-- 注意事项：
-- 1. 确保两个数据库在同一PostgreSQL实例中
-- 2. 用户需要有访问两个数据库的权限
-- 3. 跨数据库查询可能影响性能，建议添加适当的索引
-- 4. 参数化查询可以防止SQL注入攻击
-- 5. 对于大数据量查询，建议使用分页和适当的LIMIT
