# 跨数据库查询解决方案

## 问题背景

在项目中，`WaybillProduct` 模型存储在 `default` 数据库中，而 `OrdersOrderitems` 模型存储在 `orders` 数据库中。需要根据 `outer_oi_id` 字段进行关联查询。

由于 Tortoise ORM 没有 `.join()` 方法，我们提供了多种解决方案来处理跨数据库查询。

## 解决方案

### 1. 基础分步查询方法（推荐用于开发环境）

```python
async def get_orders_basic(current_user: User):
    # 第一步：查询面单商品信息
    waybill_products = await WaybillProduct.filter(
        bind_status=2,
        is_deleted=False,
        company_id=current_user.company_id,
        inbound_order_detail__shipping_user_id=current_user.id,
    ).values("id", "waybill_code", "order_id", "sku_id", "outer_oi_id")

    # 第二步：收集关联字段值
    outer_oi_ids = [item["outer_oi_id"] for item in waybill_products if item["outer_oi_id"]]
    
    # 第三步：查询订单项信息
    order_items = await OrdersOrderitems.filter(
        ex_sub_order_id__in=outer_oi_ids
    ).values("ex_sub_order_id", "raw_product_name", "sku_id", "item_num")
    
    # 第四步：合并数据
    order_items_dict = {item["ex_sub_order_id"]: item for item in order_items}
    result = []
    for waybill_item in waybill_products:
        order_item = order_items_dict.get(waybill_item["outer_oi_id"], {})
        merged_item = {**waybill_item, **order_item}
        result.append(merged_item)
    
    return result
```

**优点：**
- 兼容性好，适用于所有数据库配置
- 逻辑清晰，易于调试
- 支持复杂的过滤条件

**缺点：**
- 需要多次数据库查询
- 数据量大时性能较差

### 2. 原生SQL跨数据库查询（推荐用于生产环境）

```python
async def get_orders_with_raw_sql(current_user: User):
    # 构建跨数据库SQL查询
    sql = """
    SELECT 
        wp.id as waybill_product_id,
        wp.waybill_code,
        wp.order_id,
        wp.sku_id as waybill_sku_id,
        wp.outer_oi_id,
        oi.ex_sub_order_id,
        oi.raw_product_name,
        oi.sku_id as order_sku_id,
        oi.item_num,
        oi.goods_price
    FROM cert_waybill_product wp
    INNER JOIN cert_inbound_order_detail iod ON wp.inbound_order_detail_id = iod.id
    LEFT JOIN zhulinks_orders.orders_orderitems oi ON wp.outer_oi_id = oi.ex_sub_order_id
    WHERE wp.bind_status = $1
      AND wp.is_deleted = $2
      AND wp.company_id = $3
      AND iod.shipping_user_id = $4
    """
    
    # 执行查询
    default_db = connections.get("default")
    results = await default_db.execute_query(sql, [2, False, current_user.company_id, current_user.id])
    
    return results
```

**优点：**
- 性能最佳，单次查询完成
- 支持复杂的JOIN操作
- 可以利用数据库索引优化

**缺点：**
- 需要两个数据库在同一PostgreSQL实例中
- SQL语句较复杂，维护成本高
- 数据库名称硬编码

### 3. 使用跨数据库查询助手类

```python
from app.utils.cross_db_query import CrossDatabaseQueryHelper

async def get_orders_with_helper(current_user: User):
    results = await CrossDatabaseQueryHelper.join_models_by_field(
        primary_model=WaybillProduct,
        secondary_model=OrdersOrderitems,
        primary_filters={
            "bind_status": 2,
            "is_deleted": False,
            "company_id": current_user.company_id,
            "inbound_order_detail__shipping_user_id": current_user.id,
        },
        join_field_primary="outer_oi_id",
        join_field_secondary="ex_sub_order_id",
        primary_fields=["id", "waybill_code", "order_id", "sku_id", "outer_oi_id"],
        secondary_fields=["ex_sub_order_id", "raw_product_name", "sku_id", "item_num"],
        batch_size=1000
    )
    return results
```

**优点：**
- 封装了复杂逻辑，使用简单
- 支持批量查询优化
- 自动处理数据合并
- 提供错误处理和回退机制

**缺点：**
- 仍然是多次查询
- 需要额外的工具类维护

### 4. 复杂多表JOIN查询（推荐用于复杂业务场景）

```python
async def get_orders_with_complex_joins(current_user: User):
    # 构建包含所有相关表的完整SQL查询
    sql = f"""
    SELECT
        -- 面单商品信息
        wp.id as waybill_product_id,
        wp.waybill_code,
        wp.order_id,
        wp.sku_id as waybill_sku_id,
        wp.outer_oi_id,
        wp.product_name as waybill_product_name,

        -- 面单信息
        sw.logistics_company,
        sw.send_date,
        sw.total_qty,

        -- 入库单明细信息
        iod.code as inbound_order_detail_code,
        iod.shipping_type,
        iod.cert_type,

        -- 入库单信息
        io.code as inbound_order_code,
        io.status as inbound_order_status,

        -- 发货人信息
        su.username as shipping_username,
        su.real_name as shipping_real_name,

        -- 公司信息
        c.name as company_name,

        -- 订单项信息（跨数据库）
        oi.ex_sub_order_id,
        oi.raw_product_name,
        oi.sku_id as order_sku_id,
        oi.item_num,
        oi.goods_price,
        oi.order_status

    FROM cert_waybill_product wp
    INNER JOIN cert_shipping_waybill sw ON wp.waybill_id = sw.id
    INNER JOIN cert_inbound_order_detail iod ON sw.inbound_order_detail_id = iod.id
    INNER JOIN cert_inbound_order io ON iod.inbound_order_id = io.id
    LEFT JOIN cert_users su ON iod.shipping_user_id = su.id
    LEFT JOIN cert_users cu ON io.check_user_id = cu.id
    INNER JOIN cert_companies c ON wp.company_id = c.id
    LEFT JOIN {orders_db_name}.orders_orderitems oi ON wp.outer_oi_id = oi.ex_sub_order_id
    WHERE wp.bind_status = $1 AND wp.is_deleted = $2
      AND wp.company_id = $3 AND iod.shipping_user_id = $4
    """

    results = await default_db.execute_query(sql, [2, False, current_user.company_id, current_user.id])
    return results
```

**优点：**
- 一次查询获取所有相关信息
- 包含完整的业务数据
- 性能优秀，减少网络开销
- 支持复杂的业务逻辑

**缺点：**
- SQL语句复杂，维护成本高
- 需要跨数据库支持
- 字段较多，可能影响网络传输

### 5. SQL构建器方法（推荐用于动态查询）

```python
async def get_orders_with_builder(current_user: User):
    # 定义JOIN配置
    joins = [
        {"type": "INNER JOIN", "table": "cert_shipping_waybill", "alias": "sw", "condition": "wp.waybill_id = sw.id"},
        {"type": "INNER JOIN", "table": "cert_inbound_order_detail", "alias": "iod", "condition": "sw.inbound_order_detail_id = iod.id"},
        # ... 更多JOIN配置
    ]

    cross_db_joins = [
        {"type": "LEFT JOIN", "database": orders_db_name, "table": "orders_orderitems", "alias": "oi", "condition": "wp.outer_oi_id = oi.ex_sub_order_id"}
    ]

    # 使用构建器生成SQL
    sql = CrossDatabaseQueryHelper.build_complex_join_sql(
        base_table="cert_waybill_product wp",
        joins=joins,
        cross_db_joins=cross_db_joins,
        select_fields=select_fields,
        where_conditions=where_conditions,
        order_by=order_by
    )

    return await CrossDatabaseQueryHelper.execute_cross_db_sql("default", "orders", sql, params)
```

**优点：**
- 配置化的SQL构建
- 易于维护和扩展
- 支持动态查询条件
- 代码结构清晰

**缺点：**
- 需要学习配置语法
- 增加了抽象层复杂度

## 性能对比

| 方法 | 查询次数 | 适用场景 | 性能 | 维护性 | 复杂度 |
|------|----------|----------|------|--------|--------|
| 基础分步查询 | 2+ | 开发/小数据量 | 中等 | 高 | 低 |
| 原生SQL | 1 | 生产/大数据量 | 最佳 | 中等 | 中等 |
| 助手类 | 2+ | 通用场景 | 中等 | 高 | 低 |
| 复杂多表JOIN | 1 | 复杂业务场景 | 优秀 | 中等 | 高 |
| SQL构建器 | 1 | 动态查询 | 优秀 | 高 | 中等 |

## 使用建议

### 开发环境
推荐使用**基础分步查询方法**，因为：
- 逻辑清晰，便于调试
- 兼容性好，不依赖特定数据库配置
- 易于修改和扩展

### 生产环境
如果两个数据库在同一PostgreSQL实例中，推荐使用**原生SQL跨数据库查询**：
- 性能最佳
- 减少网络开销
- 充分利用数据库优化

如果数据库分布在不同实例中，使用**助手类方法**：
- 提供了批量查询优化
- 有完善的错误处理
- 代码复用性好

### 复杂业务场景
如果需要获取完整的业务信息，推荐使用**复杂多表JOIN查询**：
- 一次查询获取所有相关数据
- 减少应用层数据合并逻辑
- 充分利用数据库JOIN优化

### 动态查询场景
如果查询条件经常变化，推荐使用**SQL构建器方法**：
- 配置化的查询构建
- 易于添加新的JOIN表
- 支持条件动态组合

## 测试接口

项目提供了测试接口来比较不同方法的性能：

```bash
# 测试跨数据库查询能力
GET /api/v1/test/test-cross-db

# 比较不同查询方法
GET /api/v1/test/orders/compare

# 获取数据库连接信息
GET /api/v1/test/database-info

# 测试各种查询方法
GET /api/v1/test/orders/basic          # 基础分步查询
GET /api/v1/test/orders/raw-sql        # 原生SQL查询
GET /api/v1/test/orders/optimized      # 优化版查询
GET /api/v1/test/orders/helper         # 助手类查询
GET /api/v1/test/orders/complex-joins  # 复杂多表JOIN查询
GET /api/v1/test/orders/builder        # SQL构建器查询
```

## 配置要求

### 数据库配置
确保在 `local_settings.py` 中正确配置了两个数据库连接：

```python
DATABASES = {
    "default": {
        "engine": "tortoise.backends.asyncpg",
        "credentials": {
            "host": "your-host",
            "port": 5432,
            "user": "your-user",
            "password": "your-password",
            "database": "zhulinks_cert",  # 默认数据库
        },
    },
    "orders": {
        "engine": "tortoise.backends.asyncpg",
        "credentials": {
            "host": "your-host",  # 可以是同一主机
            "port": 5432,
            "user": "your-user",
            "password": "your-password",
            "database": "zhulinks_orders",  # 订单数据库
        },
    },
}
```

### 跨数据库查询要求
要使用原生SQL跨数据库查询，需要：
1. 两个数据库在同一PostgreSQL实例中
2. 用户有访问两个数据库的权限
3. 正确的数据库名称配置

## 故障排除

### 常见错误

1. **AttributeError: 'QuerySet' object has no attribute 'join'**
   - 原因：Tortoise ORM 没有 join 方法
   - 解决：使用本文档提供的替代方案

2. **跨数据库查询失败**
   - 检查数据库是否在同一实例中
   - 验证数据库名称是否正确
   - 确认用户权限

3. **性能问题**
   - 对于大数据量，使用原生SQL方法
   - 添加适当的数据库索引
   - 考虑分页查询

### 调试建议

1. 使用测试接口验证不同方法的可用性
2. 检查日志输出，了解查询执行情况
3. 监控数据库查询性能
4. 根据实际数据量选择合适的方法
