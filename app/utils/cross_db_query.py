# -*- coding: utf-8 -*-
"""
跨数据库查询工具类
用于处理不同数据库之间的关联查询
"""

from typing import Any, Dict, List

from loguru import logger
from tortoise import connections
from tortoise.models import Model


class CrossDatabaseQueryHelper:
    """
    跨数据库查询助手类
    提供多种方式处理跨数据库的关联查询
    """

    @staticmethod
    async def execute_cross_db_sql(primary_db: str, secondary_db: str, sql: str, params: List[Any] = None, fallback_to_separate_queries: bool = True) -> List[Dict[str, Any]]:
        """
        执行跨数据库SQL查询

        Args:
            primary_db: 主数据库连接名
            secondary_db: 辅助数据库连接名
            sql: SQL查询语句
            params: 查询参数
            fallback_to_separate_queries: 是否在失败时回退到分离查询

        Returns:
            查询结果列表
        """
        try:
            # 获取主数据库连接
            db_conn = connections.get(primary_db)

            # 执行跨数据库查询
            results = await db_conn.execute_query(sql, params or [])

            # 转换结果为字典列表
            if results and len(results) > 1:
                columns = [desc[0] for desc in results[1]]  # 获取列名
                data = []
                for row in results[0]:  # 获取数据行
                    data.append(dict(zip(columns, row)))
                return data

            return []

        except Exception as e:
            logger.error(f"跨数据库查询失败: {e}")
            if fallback_to_separate_queries:
                logger.info("回退到分离查询模式")
                return []
            raise

    @staticmethod
    async def join_models_by_field(
        primary_model: Model,
        secondary_model: Model,
        primary_filters: Dict[str, Any],
        join_field_primary: str,
        join_field_secondary: str,
        primary_fields: List[str] = None,
        secondary_fields: List[str] = None,
        batch_size: int = 1000,
    ) -> List[Dict[str, Any]]:
        """
        通过字段关联两个不同数据库的模型

        Args:
            primary_model: 主模型类
            secondary_model: 辅助模型类
            primary_filters: 主模型的过滤条件
            join_field_primary: 主模型的关联字段
            join_field_secondary: 辅助模型的关联字段
            primary_fields: 主模型要查询的字段
            secondary_fields: 辅助模型要查询的字段
            batch_size: 批量查询大小

        Returns:
            合并后的数据列表
        """
        # 第一步：查询主模型数据
        primary_query = primary_model.filter(**primary_filters)

        if primary_fields:
            primary_data = await primary_query.values(*primary_fields)
        else:
            primary_data = await primary_query.values()

        if not primary_data:
            return []

        # 第二步：收集关联字段值
        join_values = [item[join_field_primary] for item in primary_data if item.get(join_field_primary)]

        if not join_values:
            return primary_data

        # 第三步：分批查询辅助模型数据
        secondary_data_dict = {}

        for i in range(0, len(join_values), batch_size):
            batch_values = join_values[i : i + batch_size]

            secondary_query = secondary_model.filter(**{f"{join_field_secondary}__in": batch_values})

            if secondary_fields:
                batch_secondary_data = await secondary_query.values(*secondary_fields)
            else:
                batch_secondary_data = await secondary_query.values()

            # 建立关联字典
            for item in batch_secondary_data:
                key = item[join_field_secondary]
                secondary_data_dict[key] = item

        # 第四步：合并数据
        result = []
        for primary_item in primary_data:
            join_value = primary_item.get(join_field_primary)
            secondary_item = secondary_data_dict.get(join_value, {})

            # 合并数据，避免字段名冲突
            merged_item = {}

            # 添加主模型数据（添加前缀）
            for key, value in primary_item.items():
                merged_item[f"primary_{key}"] = value

            # 添加辅助模型数据（添加前缀）
            for key, value in secondary_item.items():
                merged_item[f"secondary_{key}"] = value

            # 添加原始数据（不带前缀，后面的会覆盖前面的）
            merged_item.update(primary_item)
            merged_item.update(secondary_item)

            # 添加关联状态
            merged_item["has_secondary_data"] = bool(secondary_item)

            result.append(merged_item)

        return result

    @staticmethod
    def build_cross_db_sql(
        primary_table: str,
        secondary_table: str,
        secondary_db_name: str,
        join_condition: str,
        primary_fields: List[str] = None,
        secondary_fields: List[str] = None,
        where_conditions: List[str] = None,
        join_type: str = "LEFT JOIN",
    ) -> str:
        """
        构建跨数据库SQL查询语句

        Args:
            primary_table: 主表名
            secondary_table: 辅助表名
            secondary_db_name: 辅助数据库名
            join_condition: 关联条件
            primary_fields: 主表字段列表
            secondary_fields: 辅助表字段列表
            where_conditions: WHERE条件列表
            join_type: 关联类型

        Returns:
            SQL查询语句
        """
        # 构建SELECT字段
        select_fields = []

        if primary_fields:
            for field in primary_fields:
                select_fields.append(f"p.{field} as primary_{field}")
        else:
            select_fields.append("p.*")

        if secondary_fields:
            for field in secondary_fields:
                select_fields.append(f"s.{field} as secondary_{field}")
        else:
            select_fields.append("s.*")

        select_clause = ", ".join(select_fields)

        # 构建FROM和JOIN子句
        from_clause = f"FROM {primary_table} p"
        join_clause = f"{join_type} {secondary_db_name}.{secondary_table} s ON {join_condition}"

        # 构建WHERE子句
        where_clause = ""
        if where_conditions:
            where_clause = f"WHERE {' AND '.join(where_conditions)}"

        # 组合完整SQL
        sql = f"""
        SELECT {select_clause}
        {from_clause}
        {join_clause}
        {where_clause}
        """.strip()

        print(sql)

        return sql

    @staticmethod
    def build_complex_join_sql(
        base_table: str,
        joins: List[Dict[str, str]],
        cross_db_joins: List[Dict[str, str]] = None,
        select_fields: List[str] = None,
        where_conditions: List[str] = None,
        order_by: List[str] = None,
        limit: int = None,
    ) -> str:
        """
        构建复杂的多表JOIN SQL查询

        Args:
            base_table: 基础表名
            joins: 同数据库内的JOIN配置列表
                   格式: [{"type": "INNER JOIN", "table": "table_name", "alias": "t1", "condition": "base.id = t1.base_id"}]
            cross_db_joins: 跨数据库JOIN配置列表
                           格式: [{"type": "LEFT JOIN", "database": "db_name", "table": "table_name", "alias": "t2", "condition": "base.id = t2.base_id"}]
            select_fields: SELECT字段列表
            where_conditions: WHERE条件列表
            order_by: 排序字段列表
            limit: 限制条数

        Returns:
            完整的SQL查询语句
        """
        # 构建SELECT子句
        if select_fields:
            select_clause = f"SELECT {', '.join(select_fields)}"
        else:
            select_clause = "SELECT *"

        # 构建FROM子句
        from_clause = f"FROM {base_table}"

        # 构建同数据库JOIN子句
        join_clauses = []
        if joins:
            for join in joins:
                join_type = join.get("type", "INNER JOIN")
                table = join["table"]
                alias = join.get("alias", "")
                condition = join["condition"]

                if alias:
                    join_clause = f"{join_type} {table} {alias} ON {condition}"
                else:
                    join_clause = f"{join_type} {table} ON {condition}"

                join_clauses.append(join_clause)

        # 构建跨数据库JOIN子句
        if cross_db_joins:
            for join in cross_db_joins:
                join_type = join.get("type", "LEFT JOIN")
                database = join["database"]
                table = join["table"]
                alias = join.get("alias", "")
                condition = join["condition"]

                if alias:
                    join_clause = f"{join_type} {database}.{table} {alias} ON {condition}"
                else:
                    join_clause = f"{join_type} {database}.{table} ON {condition}"

                join_clauses.append(join_clause)

        # 构建WHERE子句
        where_clause = ""
        if where_conditions:
            where_clause = f"WHERE {' AND '.join(where_conditions)}"

        # 构建ORDER BY子句
        order_clause = ""
        if order_by:
            order_clause = f"ORDER BY {', '.join(order_by)}"

        # 构建LIMIT子句
        limit_clause = ""
        if limit:
            limit_clause = f"LIMIT {limit}"

        # 组合完整SQL
        sql_parts = [select_clause, from_clause] + join_clauses
        if where_clause:
            sql_parts.append(where_clause)
        if order_clause:
            sql_parts.append(order_clause)
        if limit_clause:
            sql_parts.append(limit_clause)

        sql = "\n".join(sql_parts)
        return sql

    @staticmethod
    async def get_database_names() -> Dict[str, str]:
        """
        获取所有数据库连接的实际数据库名称

        Returns:
            连接名到数据库名的映射
        """
        db_names = {}

        for conn, config in connections.db_config.items():
            db_names[conn] = config["credentials"]["database"]

        return db_names

    @staticmethod
    async def test_cross_db_connection(primary_db: str = "default", secondary_db: str = "orders") -> bool:
        """
        测试跨数据库连接是否可用

        Args:
            primary_db: 主数据库连接名
            secondary_db: 辅助数据库连接名

        Returns:
            是否可以进行跨数据库查询
        """
        try:
            # 获取数据库名称
            db_names = await CrossDatabaseQueryHelper.get_database_names()

            if primary_db not in db_names or secondary_db not in db_names:
                logger.warning(f"数据库连接不存在: {primary_db}, {secondary_db}")
                return False

            # 尝试执行简单的跨数据库查询
            primary_db_name = db_names[primary_db]
            secondary_db_name = db_names[secondary_db]

            test_sql = f"""
            SELECT 1 as test_col
            FROM information_schema.tables t1
            LEFT JOIN {secondary_db_name}.information_schema.tables t2 ON t1.table_name = t2.table_name
            LIMIT 1
            """

            conn = connections.get(primary_db)
            await conn.execute_query(test_sql)

            logger.info(f"跨数据库连接测试成功: {primary_db_name} <-> {secondary_db_name}")
            return True

        except Exception as e:
            logger.error(f"跨数据库连接测试失败: {e}")
            return False
