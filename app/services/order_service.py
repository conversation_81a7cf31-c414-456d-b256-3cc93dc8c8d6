# -*- coding: utf-8 -*-
from typing import Any, Dict, List

from loguru import logger
from tortoise import connections

from app.models import OrdersOrderitems, User, WaybillProduct
from app.utils.cross_db_query import CrossDatabaseQueryHelper


class OrderService:
    @staticmethod
    async def get_orders(current_user: User):
        """
        获取当前用户绑定的面单及关联的订单项信息
        由于两个表在不同数据库，使用分步查询的方式
        """
        # 第一步：从 default 数据库查询面单商品信息
        waybill_products = await WaybillProduct.filter(
            bind_status=2,
            is_deleted=False,
            company_id=current_user.company_id,
            inbound_order_detail__shipping_user_id=current_user.id,
        ).values("id", "waybill_code", "order_id", "sku_id", "outer_oi_id", "bind_status", "product_name", "product_sn_code")

        if not waybill_products:
            return []

        # 第二步：收集所有需要查询的 outer_oi_id
        outer_oi_ids = [item["outer_oi_id"] for item in waybill_products if item["outer_oi_id"]]

        if not outer_oi_ids:
            return waybill_products

        # 第三步：从 orders 数据库查询订单项信息
        order_items = await OrdersOrderitems.filter(ex_sub_order_id__in=outer_oi_ids).values(
            "ex_sub_order_id",
            "raw_product_name",
            "sku_id",
            "item_num",
            "goods_price",
            "order_status",
            "order_status_desc",
            "order_amount",
            "pay_amount",
        )

        # 第四步：创建订单项字典，便于查找
        order_items_dict = {item["ex_sub_order_id"]: item for item in order_items}

        # 第五步：合并数据
        result = []
        for waybill_item in waybill_products:
            order_item = order_items_dict.get(waybill_item["outer_oi_id"], {})
            merged_item = {**waybill_item, **order_item}
            result.append(merged_item)

        return result

    @staticmethod
    async def get_orders_with_raw_sql(current_user: User):
        """
        使用原生SQL进行跨数据库查询
        注意：这种方法需要确保两个数据库在同一个PostgreSQL实例中
        """
        try:
            # 获取数据库名称映射
            db_names = await CrossDatabaseQueryHelper.get_database_names()
            orders_db_name = db_names.get("orders", "zhulinks_orders")

            # 使用工具类构建跨数据库SQL
            sql = CrossDatabaseQueryHelper.build_cross_db_sql(
                primary_table="cert_waybill_product",
                secondary_table="orders_orderitems",
                secondary_db_name=orders_db_name,
                join_condition="p.outer_oi_id = s.ex_sub_order_id",
                primary_fields=[
                    "id",
                    "waybill_code",
                    "order_id",
                    "sku_id",
                    "outer_oi_id",
                    "bind_status",
                    "product_name",
                    "product_sn_code",
                ],
                secondary_fields=[
                    "ex_sub_order_id",
                    "raw_product_name",
                    "sku_id",
                    "item_num",
                    "goods_price",
                    "order_status",
                    "order_status_desc",
                    "order_amount",
                    "pay_amount",
                ],
                where_conditions=[
                    "p.bind_status = $1",
                    "p.is_deleted = $2",
                    "p.company_id = $3",
                    "EXISTS (SELECT 1 FROM cert_inbound_order_detail iod WHERE iod.id = p.inbound_order_detail_id AND iod.shipping_user_id = $4)",
                ],
                join_type="LEFT JOIN",
            )

            # 执行跨数据库查询
            results = await CrossDatabaseQueryHelper.execute_cross_db_sql(
                primary_db="default",
                secondary_db="orders",
                sql=sql,
                params=[2, False, current_user.company_id, current_user.id],
                fallback_to_separate_queries=True,
            )

            if results:
                return results
            else:
                # 如果跨数据库查询失败，回退到分步查询
                logger.info("跨数据库查询无结果，回退到分步查询")
                return await OrderService.get_orders(current_user)

        except Exception as e:
            logger.error(f"跨数据库查询失败: {e}")
            # 如果跨数据库查询失败，回退到分步查询
            return await OrderService.get_orders(current_user)

    @staticmethod
    async def get_orders_with_complex_joins(current_user: User):
        """
        使用复杂的多表JOIN进行跨数据库查询
        包含所有相关表的完整信息
        """
        try:
            # 获取数据库名称映射
            db_names = await CrossDatabaseQueryHelper.get_database_names()
            orders_db_name = db_names.get("orders", "zhulinks_orders")

            # 构建完整的跨数据库SQL查询
            sql = f"""
            SELECT
                -- 关联表信息
                wp.id,
                wp.waybill_code,
                wp.order_id,
                wp.sku_id as waybill_sku_id,
                wp.outer_oi_id,
                wp.oi_id,
                wp.product_name as waybill_product_name,
                wp.quantity as waybill_quantity,
                wp.bind_status,
                wp.product_sn,
                wp.created_at as waybill_product_created_at,
                
                -- 订单表orders_order信息
                oo.shop_id as shop_id,
                oi.author_id as author_id,
                oo.order_id as inner_order_id,
                oo.order_time as order_time,
                oo.pay_time as pay_time,
                oi.order_status as order_status,
                oi.order_status_desc as order_status_desc,
                oi.raw_product_name as product_name,
                oi.raw_spec_code as spec_code,
                oi.raw_product_pic as product_image
            
            
            FROM cert_waybill_product wp
            
            -- 关联公司信息
            INNER JOIN cert_companies c ON wp.company_id = c.id

            -- 跨数据库关联订单项表
            LEFT JOIN {orders_db_name}.orders_orderitems oi ON wp.outer_oi_id = oi.ex_sub_order_id
            
            -- 跨数据库关联订单表
            LEFT JOIN {orders_db_name}.orders_order oo ON wp.order_id = oo.ex_order_id
            

            WHERE wp.bind_status = $1
              AND wp.is_deleted = $2
              AND wp.company_id = $3
              AND iod.shipping_user_id = $4

            ORDER BY wp.created_at DESC, wp.id DESC
            """

            # 执行跨数据库查询
            default_db = connections.get("default")
            print(default_db)

            results = await default_db.execute_query(sql, [2, False, current_user.company_id, current_user.id])

            # 转换结果为字典列表
            if results and len(results) > 1:
                columns = [desc[0] for desc in results[1]]  # 获取列名
                data = []
                for row in results[0]:  # 获取数据行
                    row_dict = dict(zip(columns, row))
                    # 添加一些计算字段
                    row_dict["has_order_info"] = bool(row_dict.get("order_item_id"))
                    row_dict["sku_match"] = row_dict.get("waybill_sku_id") == row_dict.get("order_sku_id") if row_dict.get("order_sku_id") else False
                    data.append(row_dict)

                logger.info(f"复杂JOIN查询成功，返回 {len(data)} 条记录")
                return data

            return []

        except Exception as e:
            logger.error(f"复杂JOIN查询失败: {e}")
            # 如果复杂查询失败，回退到基础查询
            return await OrderService.get_orders(current_user)

    @staticmethod
    async def get_orders_optimized(current_user: User) -> List[Dict[str, Any]]:
        """
        优化版本：使用批量查询减少数据库访问次数
        """
        # 第一步：查询面单商品信息（包含关联的入库单明细）
        waybill_products = (
            await WaybillProduct.filter(
                bind_status=2,
                is_deleted=False,
                company_id=current_user.company_id,
                inbound_order_detail__shipping_user_id=current_user.id,
            )
            .prefetch_related("inbound_order_detail")
            .values("id", "waybill_code", "order_id", "sku_id", "outer_oi_id", "bind_status", "product_name", "product_sn_code", "inbound_order_detail__inbound_order_code")
        )

        if not waybill_products:
            return []

        # 第二步：批量查询订单项信息
        outer_oi_ids = [item["outer_oi_id"] for item in waybill_products if item["outer_oi_id"]]

        order_items_dict = {}
        if outer_oi_ids:
            # 分批查询，避免IN子句过长
            batch_size = 1000
            for i in range(0, len(outer_oi_ids), batch_size):
                batch_ids = outer_oi_ids[i : i + batch_size]
                batch_order_items = await OrdersOrderitems.filter(ex_sub_order_id__in=batch_ids).values(
                    "ex_sub_order_id",
                    "raw_product_name",
                    "sku_id",
                    "item_num",
                    "goods_price",
                    "order_status",
                    "order_status_desc",
                    "order_amount",
                    "pay_amount",
                    "cost_price_amount",
                )

                # 更新字典
                for item in batch_order_items:
                    order_items_dict[item["ex_sub_order_id"]] = item

        # 第三步：合并数据并添加计算字段
        result = []
        for waybill_item in waybill_products:
            order_item = order_items_dict.get(waybill_item["outer_oi_id"], {})

            # 合并数据
            merged_item = {
                # 面单信息
                "waybill_product_id": waybill_item["id"],
                "waybill_code": waybill_item["waybill_code"],
                "order_id": waybill_item["order_id"],
                "waybill_sku_id": waybill_item["sku_id"],
                "outer_oi_id": waybill_item["outer_oi_id"],
                "bind_status": waybill_item["bind_status"],
                "waybill_product_name": waybill_item["product_name"],
                "product_sn_code": waybill_item["product_sn_code"],
                "inbound_order_code": waybill_item.get("inbound_order_detail__inbound_order_code"),
                # 订单项信息
                "order_product_name": order_item.get("raw_product_name"),
                "order_sku_id": order_item.get("sku_id"),
                "item_num": order_item.get("item_num"),
                "goods_price": order_item.get("goods_price"),
                "order_status": order_item.get("order_status"),
                "order_status_desc": order_item.get("order_status_desc"),
                "order_amount": order_item.get("order_amount"),
                "pay_amount": order_item.get("pay_amount"),
                "cost_price_amount": order_item.get("cost_price_amount"),
                # 计算字段
                "has_order_info": bool(order_item),
                "sku_match": waybill_item["sku_id"] == order_item.get("sku_id") if order_item else False,
            }

            result.append(merged_item)

        return result

    @staticmethod
    async def get_orders_with_helper(current_user: User) -> List[Dict[str, Any]]:
        """
        使用跨数据库查询助手类的简化方法
        """
        try:
            # 使用工具类进行关联查询
            results = await CrossDatabaseQueryHelper.join_models_by_field(
                primary_model=WaybillProduct,
                secondary_model=OrdersOrderitems,
                primary_filters={
                    "bind_status": 2,
                    "is_deleted": False,
                    "company_id": current_user.company_id,
                    "inbound_order_detail__shipping_user_id": current_user.id,
                },
                join_field_primary="outer_oi_id",
                join_field_secondary="ex_sub_order_id",
                primary_fields=["id", "waybill_code", "order_id", "sku_id", "outer_oi_id", "bind_status", "product_name", "product_sn_code"],
                secondary_fields=["ex_sub_order_id", "raw_product_name", "sku_id", "item_num", "goods_price", "order_status", "order_status_desc", "order_amount", "pay_amount"],
                batch_size=1000,
            )

            return results

        except Exception as e:
            logger.error(f"使用助手类查询失败: {e}")
            # 回退到基础方法
            return await OrderService.get_orders(current_user)

    @staticmethod
    async def test_cross_db_capability() -> Dict[str, Any]:
        """
        测试跨数据库查询能力
        """
        result = {"cross_db_available": False, "database_names": {}, "error": None}

        try:
            # 测试跨数据库连接
            result["cross_db_available"] = await CrossDatabaseQueryHelper.test_cross_db_connection()

            # 获取数据库名称
            result["database_names"] = await CrossDatabaseQueryHelper.get_database_names()

        except Exception as e:
            result["error"] = str(e)
            logger.error(f"测试跨数据库能力失败: {e}")

        return result

    @staticmethod
    async def get_orders_with_builder(current_user: User) -> List[Dict[str, Any]]:
        """
        使用SQL构建器进行复杂查询
        """
        try:
            # 获取数据库名称映射
            db_names = await CrossDatabaseQueryHelper.get_database_names()
            orders_db_name = db_names.get("orders", "zhulinks_orders")

            # 定义同数据库内的JOIN
            joins = [
                {"type": "INNER JOIN", "table": "cert_shipping_waybill", "alias": "sw", "condition": "wp.waybill_id = sw.id"},
                {"type": "INNER JOIN", "table": "cert_inbound_order_detail", "alias": "iod", "condition": "sw.inbound_order_detail_id = iod.id"},
                {"type": "INNER JOIN", "table": "cert_inbound_order", "alias": "io", "condition": "iod.inbound_order_id = io.id"},
                {"type": "LEFT JOIN", "table": "cert_users", "alias": "su", "condition": "iod.shipping_user_id = su.id"},
                {"type": "LEFT JOIN", "table": "cert_users", "alias": "cu", "condition": "io.check_user_id = cu.id"},
                {"type": "INNER JOIN", "table": "cert_companies", "alias": "c", "condition": "wp.company_id = c.id"},
            ]

            # 定义跨数据库JOIN
            cross_db_joins = [{"type": "LEFT JOIN", "database": orders_db_name, "table": "orders_orderitems", "alias": "oi", "condition": "wp.outer_oi_id = oi.ex_sub_order_id"}]

            # 定义SELECT字段
            select_fields = [
                # 面单商品信息
                "wp.id as waybill_product_id",
                "wp.waybill_code",
                "wp.order_id",
                "wp.sku_id as waybill_sku_id",
                "wp.outer_oi_id",
                "wp.product_name as waybill_product_name",
                "wp.quantity as waybill_quantity",
                "wp.bind_status",
                # 面单信息
                "sw.logistics_company",
                "sw.send_date",
                "sw.total_qty",
                # 入库单明细信息
                "iod.code as inbound_order_detail_code",
                "iod.shipping_type",
                "iod.cert_type",
                # 入库单信息
                "io.code as inbound_order_code",
                "io.status as inbound_order_status",
                # 发货人信息
                "su.username as shipping_username",
                "su.real_name as shipping_real_name",
                # 公司信息
                "c.name as company_name",
                # 订单项信息
                "oi.ex_sub_order_id",
                "oi.raw_product_name",
                "oi.sku_id as order_sku_id",
                "oi.item_num",
                "oi.goods_price",
                "oi.order_status",
                "oi.order_status_desc",
                "oi.order_amount",
                "oi.pay_amount",
            ]

            # 定义WHERE条件
            where_conditions = ["wp.bind_status = $1", "wp.is_deleted = $2", "wp.company_id = $3", "iod.shipping_user_id = $4"]

            # 定义排序
            order_by = ["wp.created_at DESC", "wp.id DESC"]

            # 构建SQL
            sql = CrossDatabaseQueryHelper.build_complex_join_sql(
                base_table="cert_waybill_product wp", joins=joins, cross_db_joins=cross_db_joins, select_fields=select_fields, where_conditions=where_conditions, order_by=order_by
            )

            # 执行查询
            results = await CrossDatabaseQueryHelper.execute_cross_db_sql(
                primary_db="default", secondary_db="orders", sql=sql, params=[2, False, current_user.company_id, current_user.id], fallback_to_separate_queries=True
            )

            if results:
                logger.info(f"SQL构建器查询成功，返回 {len(results)} 条记录")
                return results
            else:
                # 回退到基础查询
                return await OrderService.get_orders(current_user)

        except Exception as e:
            logger.error(f"SQL构建器查询失败: {e}")
            return await OrderService.get_orders(current_user)
