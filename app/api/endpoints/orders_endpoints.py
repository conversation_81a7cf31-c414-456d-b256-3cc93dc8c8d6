# -*- coding: utf-8 -*-
from fastapi import Depends
from fastapi.routing import APIRouter

from app.api.dependencies import get_current_user
from app.models import User
from app.schemas.base import RestfulResponse
from app.services.order_service import OrderService

orders_router = APIRouter()


@orders_router.get("")
async def get_orders(current_user: User = Depends(get_current_user)):
    service = OrderService()
    data = await service.get_orders_with_complex_joins(current_user)
    return RestfulResponse(data=data)
